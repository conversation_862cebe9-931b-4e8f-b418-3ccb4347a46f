#!/usr/bin/env python3
"""
AI4Mars预测效果模拟器
可以根据true mask生成不同质量的预测mask，并使用现有显示格式展示
"""

import sys
import os
import torch
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
import random
from scipy import ndimage
from skimage import morphology

# Add the current directory to Python path
sys.path.append('.')

from datasets.ai4mars import AI4Mars
from utils import ext_transforms as et

class PredictionSimulator:
    def __init__(self, quality='medium'):
        """
        初始化预测模拟器
        quality: 'excellent', 'good', 'medium', 'poor', 'very_poor'
        """
        self.quality = quality
        self.quality_params = self._get_quality_params()
        
    def _get_quality_params(self):
        """根据质量等级设置参数"""
        params = {
            'excellent': {
                'accuracy': 0.95,           # 整体准确率
                'boundary_blur': 1,         # 边界模糊程度
                'noise_level': 0.02,        # 噪声水平
                'class_confusion': 0.03,    # 类别混淆率
                'small_object_loss': 0.1,   # 小物体丢失率
            },
            'good': {
                'accuracy': 0.88,
                'boundary_blur': 2,
                'noise_level': 0.05,
                'class_confusion': 0.08,
                'small_object_loss': 0.2,
            },
            'medium': {
                'accuracy': 0.75,
                'boundary_blur': 3,
                'noise_level': 0.1,
                'class_confusion': 0.15,
                'small_object_loss': 0.3,
            },
            'poor': {
                'accuracy': 0.60,
                'boundary_blur': 5,
                'noise_level': 0.2,
                'class_confusion': 0.25,
                'small_object_loss': 0.5,
            },
            'very_poor': {
                'accuracy': 0.45,
                'boundary_blur': 8,
                'noise_level': 0.3,
                'class_confusion': 0.4,
                'small_object_loss': 0.7,
            }
        }
        return params[self.quality]
    
    def simulate_prediction(self, true_mask):
        """
        根据true mask生成模拟的预测mask
        """
        pred_mask = true_mask.copy()
        height, width = pred_mask.shape
        
        # 1. 边界模糊 - 模拟分割边界不准确
        pred_mask = self._add_boundary_blur(pred_mask)
        
        # 2. 添加噪声 - 模拟随机错误
        pred_mask = self._add_noise(pred_mask)
        
        # 3. 类别混淆 - 模拟相似类别的混淆
        pred_mask = self._add_class_confusion(pred_mask)
        
        # 4. 小物体丢失 - 模拟小区域的检测失败
        pred_mask = self._simulate_small_object_loss(pred_mask)
        
        # 5. 添加假阳性区域 - 模拟过度分割
        pred_mask = self._add_false_positives(pred_mask)
        
        return pred_mask
    
    def _add_boundary_blur(self, mask):
        """添加边界模糊效果"""
        blur_size = self.quality_params['boundary_blur']
        if blur_size <= 1:
            return mask
            
        # 对每个类别的边界进行模糊处理
        result = mask.copy()
        for class_id in np.unique(mask):
            if class_id == 255:  # 跳过ignore class
                continue
                
            class_mask = (mask == class_id).astype(np.uint8)
            
            # 腐蚀和膨胀操作模拟边界不准确
            if np.random.random() < 0.5:
                # 腐蚀 - 使区域变小
                eroded = morphology.erosion(class_mask, morphology.disk(blur_size))
                result[class_mask == 1] = 0  # 清除原区域
                result[eroded == 1] = class_id  # 设置新区域
            else:
                # 膨胀 - 使区域变大
                dilated = morphology.dilation(class_mask, morphology.disk(blur_size))
                result[dilated == 1] = class_id
                
        return result
    
    def _add_noise(self, mask):
        """添加随机噪声"""
        noise_level = self.quality_params['noise_level']
        height, width = mask.shape
        
        # 随机选择一些像素进行类别改变
        num_noise_pixels = int(height * width * noise_level)
        noise_positions = np.random.choice(height * width, num_noise_pixels, replace=False)
        
        result = mask.copy()
        for pos in noise_positions:
            y, x = divmod(pos, width)
            # 随机分配一个类别（0-4，包括背景）
            available_classes = [0, 1, 2, 3, 4]  # AI4Mars的5个类别
            result[y, x] = np.random.choice(available_classes)
            
        return result
    
    def _add_class_confusion(self, mask):
        """添加类别混淆"""
        confusion_rate = self.quality_params['class_confusion']
        
        # AI4Mars类别混淆矩阵 (哪些类别容易被混淆)
        confusion_pairs = [
            (0, 2),  # Soil <-> Sand
            (1, 3),  # Bedrock <-> Big Rock
            (2, 0),  # Sand <-> Soil
            (3, 1),  # Big Rock <-> Bedrock
        ]
        
        result = mask.copy()
        for from_class, to_class in confusion_pairs:
            class_pixels = (mask == from_class)
            if np.sum(class_pixels) == 0:
                continue
                
            # 随机选择一部分像素进行混淆
            confusion_mask = np.random.random(mask.shape) < confusion_rate
            confused_pixels = class_pixels & confusion_mask
            result[confused_pixels] = to_class
            
        return result
    
    def _simulate_small_object_loss(self, mask):
        """模拟小物体检测失败"""
        loss_rate = self.quality_params['small_object_loss']
        
        result = mask.copy()
        for class_id in np.unique(mask):
            if class_id == 255:  # 跳过ignore class
                continue
                
            # 找到该类别的所有连通区域
            class_mask = (mask == class_id).astype(np.uint8)
            labeled, num_features = ndimage.label(class_mask)
            
            for region_id in range(1, num_features + 1):
                region_mask = (labeled == region_id)
                region_size = np.sum(region_mask)
                
                # 小区域更容易丢失
                loss_prob = loss_rate * (1000 / max(region_size, 100))  # 区域越小，丢失概率越高
                loss_prob = min(loss_prob, 0.8)  # 最大丢失概率80%
                
                if np.random.random() < loss_prob:
                    # 将该区域设置为背景或相邻的主要类别
                    result[region_mask] = 4  # 设置为背景
                    
        return result
    
    def _add_false_positives(self, mask):
        """添加假阳性区域"""
        # 在背景区域随机添加一些小的预测区域
        background_mask = (mask == 4)
        if np.sum(background_mask) == 0:
            return mask
            
        result = mask.copy()
        num_false_positives = int(np.sum(background_mask) * 0.001)  # 0.1%的背景像素
        
        background_positions = np.where(background_mask)
        if len(background_positions[0]) > 0:
            selected_indices = np.random.choice(len(background_positions[0]), 
                                              min(num_false_positives, len(background_positions[0])), 
                                              replace=False)
            
            for idx in selected_indices:
                y, x = background_positions[0][idx], background_positions[1][idx]
                # 随机分配一个非背景类别
                false_class = np.random.choice([0, 1, 2, 3])
                result[y, x] = false_class
                
        return result

def display_original_style(image, true_mask, pred_mask):
    """使用原始显示格式显示结果"""
    IMAGE_SIZE = 512
    title = ["Input Image", "True Mask", "Predicted Mask"]
    
    # 创建原始布局
    fig, ax_dict = plt.subplot_mosaic(
        [
            [0, 1, 2, 3],
            [0, 1, 2, 4],
            [0, 1, 2, 5],
            [0, 1, 2, 6],
            [0, 1, 2, 7]
        ], figsize=(10, 2))
    
    # 显示图像和mask
    display_data = [image, true_mask, pred_mask]
    fsize = 10
    
    for i in range(len(display_data)):
        if i == 0:
            # Input image
            mask = ((display_data[i]) * 255).transpose(1, 2, 0).astype(np.uint8)
        else:
            # Masks - 使用AI4Mars的颜色映射
            mask = display_data[i].astype('uint8')
            mask = apply_ai4mars_colors(mask)
            
        ax_dict[i].axis("off")
        ax_dict[i].set_title(title[i], fontsize=fsize)
        ax_dict[i].imshow(mask)
    
    # 图例
    titlelbl = ["Soil", "Bed Rock", "Sand", "Big Rock", "Background"]
    colors = [(0x56, 0x94, 0x1E), (0xA0, 0x20, 0xF0), (0xCC, 0xFF, 0x42), (0xFF, 0x00, 0x00), (0x42, 0xCC, 0xFF)]
    xpos = -2
    for i in range(3, 8):
        ax_dict[i].axis("off")
        ax_dict[i].set_title(titlelbl[i-3], x=xpos, y=0.0, fontsize=fsize)
        img = Image.new(mode="RGB", size=(IMAGE_SIZE, IMAGE_SIZE), color=colors[i-3])
        ax_dict[i].imshow(img)
    
    plt.show()

def apply_ai4mars_colors(mask):
    """应用AI4Mars颜色映射"""
    # 处理255值
    mask_copy = mask.copy()
    mask_copy[mask_copy == 255] = 4
    
    # 转换为RGB
    mask_rgb = Image.fromarray(mask_copy).convert('RGB')
    mask_rgb = np.asarray(mask_rgb)
    mask_rgb = np.copy(mask_rgb)
    
    # 应用颜色
    ### Background (4) - cyan ###
    mask_rgb[mask_rgb[:, :, 0] == 4, 0] = 0x42
    mask_rgb[mask_rgb[:, :, 1] == 4, 1] = 0xCC
    mask_rgb[mask_rgb[:, :, 2] == 4, 2] = 0xFF
    ### Soil (0) - green ###
    mask_rgb[mask_rgb[:, :, 0] == 0, 0] = 0x56
    mask_rgb[mask_rgb[:, :, 1] == 0, 1] = 0x94
    mask_rgb[mask_rgb[:, :, 2] == 0, 2] = 0x1E
    ### Bedrock (1) - purple ###
    mask_rgb[mask_rgb[:, :, 0] == 1, 0] = 0xA0
    mask_rgb[mask_rgb[:, :, 1] == 1, 1] = 0x20
    mask_rgb[mask_rgb[:, :, 2] == 1, 2] = 0xF0
    ### Sand (2) - yellow ###
    mask_rgb[mask_rgb[:, :, 0] == 2, 0] = 0xCC
    mask_rgb[mask_rgb[:, :, 1] == 2, 1] = 0xFF
    mask_rgb[mask_rgb[:, :, 2] == 2, 2] = 0x42
    ### Big Rock (3) - red ###
    mask_rgb[mask_rgb[:, :, 0] == 3, 0] = 0xFF
    mask_rgb[mask_rgb[:, :, 1] == 3, 1] = 0x00
    mask_rgb[mask_rgb[:, :, 2] == 3, 2] = 0x00
    
    return mask_rgb

def interactive_demo():
    """交互式演示 - 可以选择样本和质量"""
    print("AI4Mars预测效果模拟器 - 交互模式")
    print("=" * 50)

    # 加载数据集
    data_root = './datasets/data/'
    val_transform = et.ExtCompose([
        et.ExtResize(512),
        et.ExtToTensor(),
    ])

    try:
        dataset = AI4Mars(root=data_root, split='testM3', transform=val_transform)
        print(f"数据集加载成功，共 {len(dataset)} 个样本")
    except Exception as e:
        print(f"数据集加载失败: {e}")
        return

    while True:
        print("\n" + "="*50)
        print("选择操作:")
        print("1. 随机选择样本")
        print("2. 指定样本索引")
        print("3. 退出")

        choice = input("请输入选择 (1-3): ").strip()

        if choice == '3':
            break
        elif choice == '1':
            sample_idx = np.random.randint(0, len(dataset))
        elif choice == '2':
            try:
                sample_idx = int(input(f"请输入样本索引 (0-{len(dataset)-1}): "))
                if sample_idx < 0 or sample_idx >= len(dataset):
                    print("索引超出范围!")
                    continue
            except ValueError:
                print("请输入有效的数字!")
                continue
        else:
            print("无效选择!")
            continue

        # 加载样本
        image, target = dataset[sample_idx]
        image_np = image.detach().cpu().numpy()
        target_np = target.detach().cpu().numpy() if torch.is_tensor(target) else target

        print(f"\n使用样本 {sample_idx}")
        print(f"标签中的唯一值: {np.unique(target_np)}")

        # 选择质量
        print("\n选择预测质量:")
        qualities = ['excellent', 'good', 'medium', 'poor', 'very_poor']
        for i, q in enumerate(qualities, 1):
            print(f"{i}. {q}")
        print("6. 显示所有质量对比")

        quality_choice = input("请输入选择 (1-6): ").strip()

        if quality_choice == '6':
            # 显示所有质量对比
            for quality in qualities:
                print(f"\n显示 {quality} 质量的预测效果...")
                simulator = PredictionSimulator(quality=quality)
                pred_mask = simulator.simulate_prediction(target_np)
                display_original_style(image_np, target_np, pred_mask)

                accuracy = np.mean(pred_mask == target_np)
                print(f"{quality} 质量预测准确率: {accuracy:.3f}")

                input("按Enter继续下一个质量等级...")
        else:
            try:
                quality_idx = int(quality_choice) - 1
                if 0 <= quality_idx < len(qualities):
                    quality = qualities[quality_idx]
                    print(f"\n显示 {quality} 质量的预测效果...")

                    simulator = PredictionSimulator(quality=quality)
                    pred_mask = simulator.simulate_prediction(target_np)
                    display_original_style(image_np, target_np, pred_mask)

                    accuracy = np.mean(pred_mask == target_np)
                    print(f"{quality} 质量预测准确率: {accuracy:.3f}")
                else:
                    print("无效选择!")
            except ValueError:
                print("请输入有效的数字!")

def batch_demo():
    """批量演示 - 自动展示多个样本的不同质量效果"""
    print("AI4Mars预测效果模拟器 - 批量演示")
    print("=" * 50)

    # 加载数据集
    data_root = './datasets/data/'
    val_transform = et.ExtCompose([
        et.ExtResize(512),
        et.ExtToTensor(),
    ])

    try:
        dataset = AI4Mars(root=data_root, split='testM3', transform=val_transform)
        print(f"数据集加载成功，共 {len(dataset)} 个样本")
    except Exception as e:
        print(f"数据集加载失败: {e}")
        return

    # 随机选择几个样本
    num_samples = min(3, len(dataset))
    sample_indices = np.random.choice(len(dataset), num_samples, replace=False)

    quality = 'medium'  # 使用中等质量
    print(f"使用 {quality} 质量进行批量演示")

    simulator = PredictionSimulator(quality=quality)

    for i, sample_idx in enumerate(sample_indices):
        print(f"\n样本 {i+1}/{num_samples} (索引: {sample_idx})")

        image, target = dataset[sample_idx]
        image_np = image.detach().cpu().numpy()
        target_np = target.detach().cpu().numpy() if torch.is_tensor(target) else target

        pred_mask = simulator.simulate_prediction(target_np)
        display_original_style(image_np, target_np, pred_mask)

        accuracy = np.mean(pred_mask == target_np)
        print(f"预测准确率: {accuracy:.3f}")

        if i < len(sample_indices) - 1:
            input("按Enter继续下一个样本...")

def main():
    """主函数"""
    print("AI4Mars预测效果模拟器")
    print("=" * 50)
    print("选择运行模式:")
    print("1. 交互式演示 (推荐)")
    print("2. 批量演示")
    print("3. 退出")

    choice = input("请输入选择 (1-3): ").strip()

    if choice == '1':
        interactive_demo()
    elif choice == '2':
        batch_demo()
    elif choice == '3':
        print("退出程序")
    else:
        print("无效选择!")

if __name__ == "__main__":
    main()
