from tqdm import tqdm
import network
import utils
import os
import random
import argparse
import numpy as np

from torch.utils import data
from datasets import AI4Mars, AI4MarsSMI, LabelMars6
from datasets.jiayu import JiayuDataset
# VOC and Cityscapes imports available but not commonly used in this project
# from datasets.voc import VOCSegmentation
# from datasets.cityscapes import Cityscapes
from utils import ext_transforms as et
from metrics import StreamSegMetrics

import torch
import torch.nn as nn
from utils.visualizer import Visualizer
from utils.visdomlineplotter import VisdomLinePlotter
from PIL import Image
import matplotlib
import matplotlib.pyplot as plt
from backboned_unet import Unet
#from torchmetrics import Accuracy
import copy
from sklearn.metrics import f1_score
import time
import psutil
try:
    from thop import profile, clever_format
    THOP_AVAILABLE = True
except ImportError:
    THOP_AVAILABLE = False
    print("Warning: thop not available. FLOPs calculation will be skipped.")


def get_model_flops(model, input_size, device):

    import copy
    from thop import profile

    model_copy = copy.deepcopy(model).to(device)         
    dummy = torch.randn(1, 3, input_size[0], input_size[1], device=device)

    flops, params = profile(model_copy, inputs=(dummy,), verbose=False)

  
    del model_copy
    if device.type == 'cuda':
        torch.cuda.empty_cache()

    return flops, params


class EarlyStopping:
    def __init__(self, patience=25, min_delta=0, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_model = None
        self.best_loss = None
        self.counter = 0
        self.status = ""

    def __call__(self, model, val_loss):
        vallos=0
        if self.best_loss is not None:
            vallos=val_loss - self.best_loss
            print( f"best_loss - val_loss ==> {val_loss} - {self.best_loss} = {vallos}")
        
        if self.best_loss is None:
            self.best_loss = val_loss
            self.best_model = copy.deepcopy(model.state_dict())
            print("Best Earlystop model saved....")
        elif val_loss - self.best_loss >= self.min_delta:
            self.best_model = copy.deepcopy(model.state_dict())
            print("Best Earlystop model saved....")
            self.best_loss = val_loss
            self.counter = 0
            self.status = f"Improvement found, counter reset to {self.counter}"
        else:
            self.counter += 1
            self.status = f"No improvement in the last {self.counter} epochs"
            if self.counter >= self.patience:
                self.status = f"Early stopping triggered after {self.counter} epochs."
                if self.restore_best_weights:
                    model.load_state_dict(self.best_model)
                return True
       
        return False

def get_argparser():
    parser = argparse.ArgumentParser()

    # Datset Options
    parser.add_argument("--data_root", type=str, default='./datasets/data/',
                        help="path to Dataset")
    parser.add_argument("--dataset", type=str, default='AI4Mars',
                        choices=['voc', 'cityscapes', 'AI4Mars', 'AI4Mars-SMI', 'LabelMars', 'LabelMars6', 'S5Mars', 'Jiayu'], help='Name of dataset')
    parser.add_argument("--num_classes", type=int, default=5,
                        help="num classes (default: None)")

    # Deeplab Options
    available_models = sorted(name for name in network.modeling.__dict__ if name.islower() and \
                              not (name.startswith("__") or name.startswith('_')) and callable(
                              network.modeling.__dict__[name])
                              )
    parser.add_argument("--model", type=str, default='deeplabv3plus_resnet101',#'deeplabv3plus_resnet50', #'unet_resnet101',
                        choices=available_models, help='model name')
    parser.add_argument("--separable_conv", action='store_true', default=False,
                        help="apply separable conv to decoder and aspp")
    parser.add_argument("--output_stride", type=int, default=16, choices=[8, 16])

    # Train Options
    parser.add_argument("--test_only", action='store_true', default=False)
    parser.add_argument("--save_val_results", action='store_true', default=False,
                        help="save segmentation results to \"./results\"")
    parser.add_argument("--total_epochs", type=int, default=200,
                        help="epoch number (default: 50)")
    parser.add_argument("--total_itrs", type=int, default=200000,
                        help="iteration number (default: 30k)")
    parser.add_argument("--lr", type=float, default=0.001,
                        help="learning rate (default: 0.01)")
    parser.add_argument("--lr_policy", type=str, default='poly', choices=['poly', 'step'],
                        help="learning rate scheduler policy")
    parser.add_argument("--step_size", type=int, default=10000)
    parser.add_argument("--crop_val", action='store_true', default=False,
                        help='crop validation (default: False)')
    parser.add_argument("--batch_size", type=int, default=4,
                        help='batch size (default: 16)')
    parser.add_argument("--val_batch_size", type=int, default=4,
                        help='batch size for validation (default: 4)')
    parser.add_argument("--crop_size", type=int, default=512)

    parser.add_argument("--ckpt", default= #"/media/faxir/T7/WorkstaionBackup/Unet_DeepLabV3Plus_Pytorch_V2/checkpoints_AI4Mars_GAN_23.01.2024_22.00/best_deeplabv3plus_resnet101_AI4Mars_os16.pth"
                        "checkpoints_Batchsize64_aisuureyA100/best_deeplabv3plus_resnet101_AI4Mars_os16_OrgDS_Batch64.pth"
                        #"checkpoints/best_deeplabv3plus_resnet101_LabelMars6_os16.pth"
                        #"/media/faxir/T7/WorkstaionBackup/Unet_DeepLabV3Plus_Pytorch_V2/checkpoints_03.10.2023-22.09/best_deeplabv3plus_resnet101_AI4Mars_os16.pth"
                        , type=str, help="restore from checkpoint")
     
    parser.add_argument("--continue_training", action='store_true', default=False)

    parser.add_argument("--loss_type", type=str, default='cross_entropy',
                        choices=['cross_entropy', 'focal_loss', 'dice_loss'], help="loss type (default: False)")
    parser.add_argument("--gpu_id", type=str, default='0',
                        help="GPU ID")
    parser.add_argument("--weight_decay", type=float, default=0.0001,
                        help='weight decay (default: 1e-4)')
    parser.add_argument("--random_seed", type=int, default=1,
                        help="random seed (default: 1)")
    parser.add_argument("--print_interval", type=int, default=100,
                        help="print interval of loss (default: 10)")
    parser.add_argument("--val_interval", type=int, default=1500,
                        help="epoch interval for eval (default: 1000)")
    parser.add_argument("--download", action='store_true', default=True,
                        help="download datasets")
    parser.add_argument("--test_split", type=str, default='test',
                        choices=['test', 'testM1', 'testM2', 'testM3'], 
                        help="test split to use (for AI4Mars dataset)")

    # PASCAL VOC Options
    parser.add_argument("--year", type=str, default='2012',
                        choices=['2012_aug', '2012', '2011', '2009', '2008', '2007'], help='year of VOC')

    # Visdom options
    parser.add_argument("--enable_vis", action='store_true', default=True,
                        help="use visdom for visualization")
    parser.add_argument("--vis_port", type=str, default='8097',
                        help='port for visdom')
    parser.add_argument("--vis_env", type=str, default='main', 
                        help='env for visdom')
    parser.add_argument("--vis_num_samples", type=int, default=8,
                        help='number of samples for visualization (default: 8)')
    
    # Performance Evaluation Options
    parser.add_argument("--eval_performance", action='store_true', default=False,
                        help="evaluate model performance (FPS, FLOPs, Params) during test")

    # Display Options
    parser.add_argument("--show_display", action='store_true', default=False,
                        help="show visual comparison of input image, true mask, and predicted mask during evaluation")
    return parser

def get_dataset(opts):
    """ Dataset And Augmentation
    """
    if opts.dataset == 'voc':
        train_transform = et.ExtCompose([
            # et.ExtResize(size=opts.crop_size),
            et.ExtRandomScale((0.5, 2.0)),
            et.ExtRandomCrop(size=(opts.crop_size, opts.crop_size), pad_if_needed=True),
            et.ExtRandomHorizontalFlip(),
            et.ExtToTensor(),
            et.ExtNormalize(mean=[0.485, 0.456, 0.406],
                            std=[0.229, 0.224, 0.225]),
        ])
        if opts.crop_val:
            val_transform = et.ExtCompose([
                et.ExtResize(opts.crop_size),
                et.ExtCenterCrop(opts.crop_size),
                et.ExtToTensor(),
                et.ExtNormalize(mean=[0.485, 0.456, 0.406],
                                std=[0.229, 0.224, 0.225]),
            ])
        else:
            val_transform = et.ExtCompose([
                et.ExtToTensor(),
                et.ExtNormalize(mean=[0.485, 0.456, 0.406],
                                std=[0.229, 0.224, 0.225]),
            ])
        train_dst = VOCSegmentation(root=opts.data_root, year=opts.year,
                                    image_set='train', download=opts.download, transform=train_transform)
        val_dst = VOCSegmentation(root=opts.data_root, year=opts.year,
                                  image_set='val', download=opts.download, transform=val_transform)

    if opts.dataset == 'cityscapes':
        train_transform = et.ExtCompose([
            # et.ExtResize( 512 ),
            et.ExtRandomCrop(size=(opts.crop_size, opts.crop_size)),
            et.ExtColorJitter(brightness=0.5, contrast=0.5, saturation=0.5),
            et.ExtRandomHorizontalFlip(),
            et.ExtToTensor(),
            et.ExtNormalize(mean=[0.485, 0.456, 0.406],
                            std=[0.229, 0.224, 0.225]),
        ])

        val_transform = et.ExtCompose([
            # et.ExtResize( 512 ),
            et.ExtToTensor(),
            et.ExtNormalize(mean=[0.485, 0.456, 0.406],
                            std=[0.229, 0.224, 0.225]),
        ])

        train_dst = Cityscapes(root=opts.data_root,
                               split='train', transform=train_transform)
        val_dst = Cityscapes(root=opts.data_root,
                             split='val', transform=val_transform)
        
    if opts.dataset == 'AI4Mars':
        train_transform = et.ExtCompose([
            et.ExtResize(opts.crop_size),
            # et.ExtResize( 512 ),
            # et.ExtRandomCrop(size=(opts.crop_size, opts.crop_size)),
            # et.ExtColorJitter(brightness=0.5, contrast=0.5, saturation=0.5),
            # et.ExtRandomHorizontalFlip(),
            et.ExtToTensor(),
            # et.ExtNormalize(mean=[0.485, 0.456, 0.406],
            #                 std=[0.229, 0.224, 0.225]),
        ])

        val_transform = et.ExtCompose([
            et.ExtResize(opts.crop_size),
            # et.ExtResize( 512 ),
            et.ExtToTensor(),
             # et.ExtNormalize(mean=[0.485, 0.456, 0.406],
             #                 std=[0.229, 0.224, 0.225]),
        ])
        
        train_dst = AI4Mars(root=opts.data_root,
                                split='train', transform=train_transform)
        val_dst = AI4Mars(root=opts.data_root,
                              split=opts.test_split if opts.test_only else 'val', transform=val_transform)
    
    if opts.dataset == 'AI4Mars-SMI':
        train_transform = et.ExtCompose([
            et.ExtResize(opts.crop_size),
            # et.ExtRandomCrop(size=(opts.crop_size, opts.crop_size)),
            # et.ExtColorJitter(brightness=0.5, contrast=0.5, saturation=0.5),
            # et.ExtRandomHorizontalFlip(),
            et.ExtToTensor(),
            # et.ExtNormalize(mean=[0.485, 0.456, 0.406],
            #                 std=[0.229, 0.224, 0.225]),
        ])

        val_transform = et.ExtCompose([
            et.ExtResize(opts.crop_size),
            et.ExtToTensor(),
            # et.ExtNormalize(mean=[0.485, 0.456, 0.406],
            #                 std=[0.229, 0.224, 0.225]),
        ])
        
        train_dst = AI4MarsSMI(root=opts.data_root,
                                split='train', transform=train_transform)
        val_dst = AI4MarsSMI(root=opts.data_root,
                              split=opts.test_split if opts.test_only else 'val', transform=val_transform)
    
    if opts.dataset == 'LabelMars':
        train_transform = et.ExtCompose([
            et.ExtResize(opts.crop_size),
            # et.ExtResize( 512 ),
            #et.ExtRandomCrop(size=(opts.crop_size, opts.crop_size)),
            #et.ExtColorJitter(brightness=0.5, contrast=0.5, saturation=0.5),
            #et.ExtRandomHorizontalFlip(),
            et.ExtToTensor(),
            # et.ExtNormalize(mean=[0.485, 0.456, 0.406],
            #                 std=[0.229, 0.224, 0.225]),
        ])

        val_transform = et.ExtCompose([
            et.ExtResize(opts.crop_size),
            # et.ExtResize( 512 ),
            et.ExtToTensor(),
            # et.ExtNormalize(mean=[0.485, 0.456, 0.406],
            #                 std=[0.229, 0.224, 0.225]),
        ])

        train_dst = LabelMars(root=opts.data_root,
                               split='train', transform=train_transform)
        val_dst = LabelMars(root=opts.data_root,
                             split='test', transform=val_transform)
        
    if opts.dataset == 'S5Mars':
        
        train_transform = et.ExtCompose([
            et.ExtResize(opts.crop_size),
            #et.ExtResize( 512 ),
            #et.ExtRandomCrop(size=(opts.crop_size, opts.crop_size)),
            #et.ExtColorJitter(brightness=0.5, contrast=0.5, saturation=0.5),
            #et.ExtRandomHorizontalFlip(),
            et.ExtToTensor(),
            # et.ExtNormalize(mean=[0.485, 0.456, 0.406],
            #                 std=[0.229, 0.224, 0.225]),
        ])

        val_transform = et.ExtCompose([
            et.ExtResize(opts.crop_size),
            #et.ExtResize( 512 ),
            et.ExtToTensor(),
            # et.ExtNormalize(mean=[0.485, 0.456, 0.406],
            #                 std=[0.229, 0.224, 0.225]),
        ])
        
        train_dst = S5Mars(root=opts.data_root,
                               split='train', transform=train_transform)
        val_dst = S5Mars(root=opts.data_root,
                             split='test', transform=val_transform)
        
    if opts.dataset == 'LabelMars6':
        
        train_transform = et.ExtCompose([
            et.ExtResize(opts.crop_size),
            # et.ExtResize( 512 ),
            #et.ExtRandomCrop(size=(opts.crop_size, opts.crop_size)),
            #et.ExtColorJitter(brightness=0.5, contrast=0.5, saturation=0.5),
            #et.ExtRandomHorizontalFlip(),
            et.ExtToTensor(),
            # et.ExtNormalize(mean=[0.485, 0.456, 0.406],
            #                 std=[0.229, 0.224, 0.225]),
        ])

        val_transform = et.ExtCompose([
            et.ExtResize(opts.crop_size),
            # et.ExtResize( 512 ),
            et.ExtToTensor(),
            # et.ExtNormalize(mean=[0.485, 0.456, 0.406],
            #                 std=[0.229, 0.224, 0.225]),
        ])
        
        train_dst = LabelMars6(root=opts.data_root,
                               split='train', transform=train_transform)
        val_dst = LabelMars6(root=opts.data_root,
                             split='test', transform=val_transform)

    if opts.dataset == 'Jiayu':
        train_transform = et.ExtCompose([
            et.ExtResize(opts.crop_size),
            et.ExtRandomCrop(size=(opts.crop_size, opts.crop_size), pad_if_needed=True),
            et.ExtRandomHorizontalFlip(),
            et.ExtToTensor(),
            et.ExtNormalize(mean=[0.485, 0.456, 0.406],
                            std=[0.229, 0.224, 0.225]),
        ])

        val_transform = et.ExtCompose([
            et.ExtResize(opts.crop_size),
            et.ExtToTensor(),
            et.ExtNormalize(mean=[0.485, 0.456, 0.406],
                            std=[0.229, 0.224, 0.225]),
        ])

        train_dst = JiayuDataset(root=opts.data_root,
                                split='train', transform=train_transform)
        val_dst = JiayuDataset(root=opts.data_root,
                              split=opts.test_split if opts.test_only else 'val', transform=val_transform)

    return train_dst, val_dst

def validateSMI(opts, model, loader, device, metrics, ret_samples_ids=None):
    """Do validation and return specified samples"""
    metrics.reset()
    ret_samples = []
    if opts.save_val_results:
        if not os.path.exists('results'):
            os.mkdir('results')
        if not os.path.exists('results/images'):
            os.mkdir('results/images')
        if not os.path.exists('results/labels'):
            os.mkdir('results/labels')
        # denorm = utils.Denormalize(mean=[0.485, 0.456, 0.406],
        #                            std=[0.229, 0.224, 0.225])
        img_id = 0

    with torch.no_grad():
        for i, (images, labels) in tqdm(enumerate(loader)):

            images = images.to(device, dtype=torch.float32)
            labels = labels.to(device, dtype=torch.long)

            outputs = model(images)
            preds = outputs.detach().max(dim=1)[1].cpu().numpy()
            targets = labels.cpu().numpy()

            metrics.update(targets, preds)
            if ret_samples_ids is not None and i in ret_samples_ids:  # get vis samples
                ret_samples.append(
                    (images[0].detach().cpu().numpy(), targets[0], preds[0]))
            
            if opts.save_val_results:
                for i in range(len(images)):
                    image = images[i].detach().cpu().numpy()
                    target = targets[i] 
                    pred = preds[i]

                    image = ((image) * 255).transpose(1, 2, 0).astype(np.uint8)#denorm
                    # target[target == 255] = 4
                    # target = target.astype(np.uint8)
                    #target = loader.dataset.decode_target(target).astype(np.uint8)
                    pred[pred == 255] = 4
                    pred = pred.astype(np.uint8)
                    #pred = loader.dataset.decode_target(pred).astype(np.uint8)

                    Image.fromarray(image).save('results/images/%d.JPG' % img_id)
                    Image.fromarray(target).save('results/images/%d_target.png' % img_id)
                    Image.fromarray(pred).save('results/labels/%d.png' % img_id)

                    fig = plt.figure()
                    plt.imshow(image)
                    plt.axis('off')
                    plt.imshow(pred, alpha=0.7)
                    ax = plt.gca()
                    ax.xaxis.set_major_locator(matplotlib.ticker.NullLocator())
                    ax.yaxis.set_major_locator(matplotlib.ticker.NullLocator())
                    plt.savefig('results/images/%d_overlay.png' % img_id, bbox_inches='tight', pad_inches=0)
                    plt.close()
                    img_id += 1
            
        score = metrics.get_results()
    return score, ret_samples

def validate(opts, model, loader, device, metrics, ret_samples_ids=None):
    """Do validation and return specified samples"""
    metrics.reset()
    ret_samples = []
    if opts.save_val_results:
        if not os.path.exists('results'):
            os.mkdir('results')
        if not os.path.exists('results/images'):
            os.mkdir('results/images')
        if not os.path.exists('results/labels'):
            os.mkdir('results/labels')
        # denorm = utils.Denormalize(mean=[0.485, 0.456, 0.406],
        #                            std=[0.229, 0.224, 0.225])
        img_id = 0

    with torch.no_grad():
        for i, (images, labels) in tqdm(enumerate(loader)):

            images = images.to(device, dtype=torch.float32)
            labels = labels.to(device, dtype=torch.long)

            outputs = model(images)
            preds = outputs.detach().max(dim=1)[1].cpu().numpy()
            targets = labels.cpu().numpy()

            metrics.update(targets, preds)
            if ret_samples_ids is not None and i in ret_samples_ids:  # get vis samples
                ret_samples.append(
                    (images[0].detach().cpu().numpy(), targets[0], preds[0]))
            
            if opts.save_val_results:
                for i in range(len(images)):
                    image = images[i].detach().cpu().numpy()
                    target = targets[i] 
                    pred = preds[i]

                    image = ((image) * 255).transpose(1, 2, 0).astype(np.uint8)#denorm
                    # target[target == 255] = 4
                    # target = target.astype(np.uint8)
                    #target = loader.dataset.decode_target(target).astype(np.uint8)
                    pred[pred == 255] = 4
                    pred = pred.astype(np.uint8)
                    #pred = loader.dataset.decode_target(pred).astype(np.uint8)

                    Image.fromarray(image).save('results/images/%d_image.png' % img_id)
                    #Image.fromarray(target).save('results/%d_target.png' % img_id)
                    Image.fromarray(pred).save('results/labels/%d_pred.png' % img_id)

                    #fig = plt.figure()
                    # plt.imshow(image)
                    # plt.axis('off')
                    # plt.imshow(pred, alpha=0.7)
                    # ax = plt.gca()
                    # ax.xaxis.set_major_locator(matplotlib.ticker.NullLocator())
                    # ax.yaxis.set_major_locator(matplotlib.ticker.NullLocator())
                    # plt.savefig('results/%d_overlay.png' % img_id, bbox_inches='tight', pad_inches=0)
                    # plt.close()
                    img_id += 1
            
        score = metrics.get_results()
    return score, ret_samples

def validateLoss(criterion, model, loader, device, metrics):
    """Do validation and return specified samples"""
    metrics.reset()
    interval_loss=0
    with torch.no_grad():
        for i, (images, labels) in tqdm(enumerate(loader)):
            images = images.to(device, dtype=torch.float32)
            labels = labels.to(device, dtype=torch.long)

            outputs = model(images)
            loss = criterion(outputs, labels)
            np_loss = loss.detach().cpu().numpy()
            interval_loss += np_loss
            
            preds = outputs.detach().max(dim=1)[1].cpu().numpy()
            targets = labels.cpu().numpy()

            metrics.update(targets, preds)
        score = metrics.get_results()
        interval_loss = interval_loss / (i + 1)
    return score, interval_loss

def validateShow(criterion, model, loader, device, metrics):
    """Do validation with visual display and return evaluation metrics"""
    metrics.reset()
    interval_loss = 0
    model.eval()

    # Store all samples with their IoU scores for sorting
    samples_with_scores = []

    with torch.no_grad():
        for i, (images, labels) in tqdm(enumerate(loader)):
            images = images.to(device, dtype=torch.float32)
            labels = labels.to(device, dtype=torch.long)

            outputs = model(images)
            loss = criterion(outputs, labels)

            preds = outputs.detach().max(dim=1)[1].cpu().numpy()
            targets = labels.detach().cpu().numpy()

            # Calculate IoU for each sample in the batch
            for j in range(len(images)):
                sample_pred = preds[j]
                sample_target = targets[j]
                sample_image = images[j]

                # Calculate per-sample IoU
                sample_iou = calculate_sample_iou(sample_target, sample_pred, metrics.n_classes)

                samples_with_scores.append({
                    'image': sample_image,
                    'target': sample_target,
                    'pred': sample_pred,
                    'iou': sample_iou,
                    'loss': loss.item()
                })

            # Update metrics for evaluation
            metrics.update(targets, preds)
            interval_loss += loss.item()

        # Sort samples by IoU (highest first)
        samples_with_scores.sort(key=lambda x: x['iou'], reverse=True)

        # Display samples in order of IoU
        print(f"Displaying {len(samples_with_scores)} samples sorted by IoU (highest first)...")
        for idx, sample in enumerate(samples_with_scores):
            print(f"Sample {idx+1}/{len(samples_with_scores)} - IoU: {sample['iou']:.4f}")
            display_single_sample(sample['image'], sample['target'], sample['pred'], loader)

        score = metrics.get_results()
        interval_loss = interval_loss / len(samples_with_scores)
    return score, interval_loss

def calculate_sample_iou(target, pred, n_classes):
    """Calculate IoU for a single sample"""
    ious = []
    for cls in range(n_classes):
        target_cls = (target == cls)
        pred_cls = (pred == cls)

        if target_cls.sum() == 0 and pred_cls.sum() == 0:
            ious.append(1.0)  # Perfect match for absent class
        elif target_cls.sum() == 0:
            ious.append(0.0)  # False positive
        else:
            intersection = (target_cls & pred_cls).sum()
            union = (target_cls | pred_cls).sum()
            ious.append(intersection / union if union > 0 else 0.0)

    return np.mean(ious)  # Mean IoU for this sample

IMAGE_SIZE = 512

def get_dataset_info(dataset):
    """Extract class information from dataset automatically"""
    dataset_name = dataset.__class__.__name__

    # Try to get class information from dataset
    if hasattr(dataset, 'get_class_names') and hasattr(dataset, 'get_class_colors'):
        # Jiayu dataset style
        class_names = dataset.get_class_names()
        class_colors = dataset.get_class_colors()
        num_classes = len(class_names)
    elif hasattr(dataset, 'classes') and hasattr(dataset, 'train_id_to_color'):
        # AI4Mars/LabelMars style
        valid_classes = [c for c in dataset.classes if c.train_id != -1 and c.train_id != 255]
        class_names = [c.name for c in valid_classes]
        # Get colors corresponding to valid train_ids
        class_colors = []
        for c in valid_classes:
            if c.train_id < len(dataset.train_id_to_color):
                class_colors.append(dataset.train_id_to_color[c.train_id].tolist())
            else:
                class_colors.append([128, 128, 128])  # Gray fallback
        num_classes = len(class_names)
    else:
        # Fallback: generate automatic colors
        num_classes = getattr(dataset, 'num_classes', 5)  # Default to 5 if not found
        class_names = [f'Class {i}' for i in range(num_classes)]
        class_colors = generate_colors(num_classes)

    return class_names, class_colors, num_classes, dataset_name

def generate_colors(num_classes):
    """Generate distinct colors for visualization"""
    import colorsys
    colors = []
    for i in range(num_classes):
        hue = i / num_classes
        saturation = 0.8
        value = 0.9
        rgb = colorsys.hsv_to_rgb(hue, saturation, value)
        colors.append([int(rgb[0] * 255), int(rgb[1] * 255), int(rgb[2] * 255)])
    return colors

def apply_color_mapping(mask_array, class_colors):
    """Apply color mapping to mask array based on class IDs"""
    # Create RGB output
    height, width = mask_array.shape
    colored_mask = np.zeros((height, width, 3), dtype=np.uint8)

    # Apply colors for each class
    for class_id, color in enumerate(class_colors):
        mask_pixels = (mask_array == class_id)
        colored_mask[mask_pixels] = color

    return colored_mask

def display(display_list, loader):
    """Universal display function that automatically adapts to any dataset"""
    title = ["Input Image", "True Mask", "Predicted Mask"]

    # Get dataset information
    class_names, class_colors, num_classes, dataset_name = get_dataset_info(loader.dataset)

    print(f"Dataset: {dataset_name}")
    print(f"Classes: {class_names}")
    print(f"Colors: {class_colors}")

    for j in range(len(display_list[0])):
        # Create figure with appropriate layout
        if num_classes <= 3:
            # Simple layout for datasets with few classes
            fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        else:
            # Layout with legend space for datasets with many classes
            fig = plt.figure(figsize=(16, 6))
            gs = fig.add_gridspec(1, 4, width_ratios=[1, 1, 1, 0.3])
            axes = [fig.add_subplot(gs[0, i]) for i in range(3)]
            legend_ax = fig.add_subplot(gs[0, 3])
            legend_ax.axis('off')

        for i in range(len(display_list)):
            if i == 0:
                # Input image - denormalize and convert to uint8
                img_tensor = display_list[i][j].detach().cpu().numpy()
                if img_tensor.shape[0] == 3:  # CHW format
                    img_array = img_tensor.transpose(1, 2, 0)
                else:  # HWC format
                    img_array = img_tensor

                # Denormalize (assuming ImageNet normalization)
                mean = np.array([0.485, 0.456, 0.406])
                std = np.array([0.229, 0.224, 0.225])
                img_array = img_array * std + mean
                img_array = np.clip(img_array * 255, 0, 255).astype(np.uint8)

                display_img = img_array
            else:
                # True mask or predicted mask - apply color mapping
                mask_array = display_list[i][j].astype(np.uint8)
                display_img = apply_color_mapping(mask_array, class_colors)

                # Debug: print unique values in mask
                unique_values = np.unique(mask_array)
                print(f"{title[i]} unique values: {unique_values}")

            axes[i].axis("off")
            axes[i].set_title(title[i], fontsize=12, fontweight='bold')
            axes[i].imshow(display_img)

        # Create legend
        from matplotlib.patches import Patch
        legend_elements = []
        for class_id, (name, color) in enumerate(zip(class_names, class_colors)):
            legend_elements.append(
                Patch(facecolor=np.array(color)/255.0, label=f'{class_id}: {name}')
            )

        if num_classes <= 3:
            fig.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98))
        else:
            legend_ax.legend(handles=legend_elements, loc='center', frameon=False)
            legend_ax.set_title('Classes', fontweight='bold')

        plt.tight_layout()
        plt.show()

        # Wait for user input to continue to next image (optional)
        try:
            input("Press Enter to continue to next image (or Ctrl+C to stop)...")
        except KeyboardInterrupt:
            print("\nVisualization stopped by user.")
            break
 
# def create_mask(pred_mask):
#   pred_mask = tf.argmax(pred_mask, axis=-1)
#   pred_mask = pred_mask[..., tf.newaxis]
#   return pred_mask

def move_to_device_recursive(module, target_device):
    """Recursively move all parameters and buffers of a module to the target device"""
    for child in module.children():
        move_to_device_recursive(child, target_device)
    # Move parameters and buffers of current module
    for param in module.parameters(recurse=False):
        if param.device != target_device:
            param.data = param.data.to(target_device)
    for buffer in module.buffers(recurse=False):
        if buffer.device != target_device:
            buffer.data = buffer.data.to(target_device)

def evaluate_model_performance(model, device, val_loader=None, input_size=(512, 512), batch_size=8, iterations=100):
    """
    Evaluate model performance metrics: FPS, FLOPs, Params

    Args:
        model: Model to be evaluated
        device: Device (cuda/cpu)
        input_size: Input image size (height, width)
        batch_size: Batch size
        iterations: Number of iterations for FPS testing

    Returns:
        dict: Dictionary containing FPS, FLOPs, Params
    """
    model.eval()

    # Ensure model is completely on the specified device
    model = model.to(device)

    # Recursively ensure all components are on the correct device
    move_to_device_recursive(model, device)

    # Get original model for FLOPs calculation if it's a DataParallel model
    original_model = model.module if hasattr(model, 'module') else model

    # Calculate parameter count
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    params_mb = total_params * 4 / (1024 * 1024)  # Assume float32, 4 bytes per parameter

    # Remove redundant titles and separators
    
    # ---------------------------------------------------------------------
    # FLOPs calculation：调用我们刚写的 get_model_flops()
    # ---------------------------------------------------------------------
    flops = None
    if THOP_AVAILABLE:
        try:
            flops, _ = get_model_flops(original_model, input_size, device)
        except Exception as e:
            print(f"[THOP] FLOPs 计算失败：{e}")
            flops = None

    else:
        flops = None
    
    # Clear GPU cache and synchronize
    if device.type == 'cuda':
        torch.cuda.empty_cache()
        torch.cuda.synchronize()

    # Select test data source
    if val_loader is not None:
        # Use current real dataset for performance testing
        print(f"Using real dataset for performance evaluation...")

        # Collect all real data batches
        real_batches = []
        for images, _ in val_loader:
            real_batches.append(images.to(device, dtype=torch.float32))

        # Use all batches from real dataset for testing
        test_data = real_batches
        actual_iterations = len(real_batches)
        print(f"Using {actual_iterations} real batches from the dataset")
    else:
        # Use synthetic data (original method)
        print("Using synthetic data for performance evaluation...")
        test_input = torch.randn(batch_size, 3, input_size[0], input_size[1]).to(device)
        test_data = [test_input] * iterations
        actual_iterations = iterations

    # Warmup (silent)
    try:
        with torch.no_grad():
            for i in range(min(10, len(test_data))):
                test_input = test_data[i % len(test_data)]
                if test_input.device != device:
                    test_input = test_input.to(device)
                output = model(test_input)
                del output
    except Exception as e:
        # Silent retry warmup
        model = model.to(device)
        move_to_device_recursive(model, device)
        with torch.no_grad():
            for i in range(min(5, len(test_data))):
                test_input = test_data[i % len(test_data)]
                output = model(test_input)
                del output

    # Synchronize GPU
    if device.type == 'cuda':
        torch.cuda.synchronize()

    # FPS testing
    start_time = time.time()

    with torch.no_grad():
        for i in range(actual_iterations):
            test_input = test_data[i]
            output = model(test_input)
            del output

    # Synchronize GPU
    if device.type == 'cuda':
        torch.cuda.synchronize()

    end_time = time.time()

    # Calculate FPS
    total_time = end_time - start_time
    total_images = actual_iterations * batch_size
    fps = total_images / total_time

    # Output simplified results (similar to reference paper format)
    params_m = total_params / 1e6  # Convert to millions
    flops_g = flops / 1e9 if flops is not None else None  # Convert to billions

    print(f"\nModel Performance:")
    print(f"  FPS: {fps:.1f}")
    if flops_g is not None:
        print(f"  FLOPs: {flops_g:.1f}G")
    else:
        print(f"  FLOPs: N/A")
    print(f"  Params: {params_m:.2f}M")

    # Return results
    results = {
        'fps': fps,
        'params': total_params,
        'params_mb': params_mb,
        'flops': flops if THOP_AVAILABLE else None,
        'gpu_memory_gb': torch.cuda.max_memory_allocated() / (1024**3) if device.type == 'cuda' else None
    }

    return results

def main():  
    opts = get_argparser().parse_args()
    if opts.dataset.lower() == 'voc':
        opts.num_classes = 21
    elif opts.dataset.lower() == 'cityscapes':
        opts.num_classes = 19
    elif opts.dataset.lower() == 'ai4mars':
        opts.num_classes = 5
    elif opts.dataset.lower() == 'labelmars':
        opts.num_classes = 25
    elif opts.dataset.lower() == 'labelmars6':
        opts.num_classes = 6
    elif opts.dataset.lower() == 's5mars':
        opts.num_classes = 10
    elif opts.dataset.lower() == 'jiayu':
        opts.num_classes = 2

    # Setup visualization
    vis = Visualizer(port=opts.vis_port,
                     env=opts.vis_env) if opts.enable_vis else None
    visplotter = VisdomLinePlotter(env_name=opts.vis_env) if opts.enable_vis else None

    if vis is not None:  # display options
        vis.vis_table("Options", vars(opts))
        
    os.environ['CUDA_VISIBLE_DEVICES'] = opts.gpu_id
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print("Device: %s" % device)

    # Setup random seed
    torch.manual_seed(opts.random_seed)
    np.random.seed(opts.random_seed)
    random.seed(opts.random_seed)

    # Setup dataloader
    if opts.dataset == 'voc' and not opts.crop_val:
        opts.val_batch_size = 1

    train_dst, val_dst = get_dataset(opts)
    train_loader = data.DataLoader(
        train_dst, batch_size=opts.batch_size, shuffle=True, num_workers=2,
        drop_last=True)  # drop_last=True to ignore single-image batches.
    val_loader = data.DataLoader(
        val_dst, batch_size=opts.val_batch_size, shuffle=True, num_workers=2)

    print("Dataset: %s, Train set: %d, Val set: %d" %
          (opts.dataset, len(train_dst), len(val_dst)))
    
    # Epoches ......................................
    print(len(train_dst))
    iterations=len(train_dst)/opts.batch_size
    print(f"Iterations = {iterations}")
    opts.total_itrs = int(opts.total_epochs * iterations)+1
    #...............................................
    
    if opts.model == 'unet_resnet50':
        backbone_name = 'resnet50'
    elif  opts.model == 'unet_resnet101':
        backbone_name = 'resnet101'
    
    #model = Unet(backbone_name=backbone_name, classes=opts.num_classes)
    # Set up model (all models are 'constructed at network.modeling)
    model = network.modeling.__dict__[opts.model](num_classes=opts.num_classes, output_stride=opts.output_stride)
    
    if opts.separable_conv and 'plus' in opts.model:
        network.convert_to_separable_conv(model.classifier)

    # Special handling for new models' backbone BN layer momentum setting
    if any(model_type in opts.model for model_type in ['segformer', 'mask2former', 'upernet']):
        # New models may not have traditional backbone structure, need safe BN momentum setting
        if hasattr(model, 'backbone') and model.backbone is not None:
            try:
                utils.set_bn_momentum(model.backbone, momentum=0.01)
            except:
                print(f"Warning: Could not set BN momentum for {opts.model}")
    else:
        # Traditional DeepLabV3+ models
        utils.set_bn_momentum(model.backbone, momentum=0.01)

    # Set up metrics
    metrics = StreamSegMetrics(opts.num_classes)

    # Set up optimizer
    if any(model_type in opts.model for model_type in ['segformer', 'mask2former', 'upernet']):
        # New models use AdamW optimizer with reduced learning rate
        optimizer = torch.optim.AdamW(params=model.parameters(),
                                   lr=opts.lr * 0.1,  # Reduce learning rate
                                   weight_decay=opts.weight_decay)
        print(f"Using AdamW optimizer, learning rate: {opts.lr * 0.1}")
    else:
        # Traditional DeepLabV3+ models use SGD optimizer
        backbonepar=model.backbone.parameters()
        #backbonecls=model.classifier.parameters()
        params=[
             {'params': backbonepar, 'lr': opts.lr}#,
             #{'params': backbonecls, 'lr': opts.lr}
         ]
        #optimizer = torch.optim.SGD(params=params, lr=opts.lr, momentum=0.9, weight_decay=opts.weight_decay)
        #print(optimizer)
        optimizer = torch.optim.SGD(params=model.parameters(), lr=opts.lr, momentum=0.9, weight_decay=opts.weight_decay)
        #optimizer = torch.optim.Adam(params=model.parameters())
        print(f"Using SGD optimizer, learning rate: {opts.lr}")
    # torch.optim.lr_scheduler.StepLR(optimizer, step_size=opts.lr_decay_step, gamma=opts.lr_decay_factor)
    if opts.lr_policy == 'poly':
        scheduler = utils.PolyLR(optimizer, opts.total_itrs, power=0.9)
    elif opts.lr_policy == 'step':
        scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=opts.step_size, gamma=0.1)

    # Set up criterion
    # criterion = utils.get_loss(opts.loss_type)
    if opts.loss_type == 'focal_loss':
        criterion = utils.FocalLoss(ignore_index=9, size_average=True)
    elif opts.loss_type == 'cross_entropy':
        #weights = torch.tensor([0.001, 0.001, 0.001, 0.97, 0.0], device='cuda:0')
        criterion = nn.CrossEntropyLoss(reduction='mean', ignore_index=255)#, weight=weights)    
    elif opts.loss_type == 'dice_loss':
        weights = torch.tensor([1, 1, 1, 1, 0.0], device='cuda:0')
        criterion = utils.DiceLoss(ignore_index=5, weight=weights)
        
    def save_ckpt(path):
        """ save current model
        """
        torch.save({
            "cur_epochs": cur_epochs,
            "cur_itrs": cur_itrs,
            "model_state": model.module.state_dict(),
            "optimizer_state": optimizer.state_dict(),
            "scheduler_state": scheduler.state_dict(),
            "best_score": best_score,
        }, path)
        print("Model saved as %s" % path)

    utils.mkdir('checkpoints')
    # Restore
    best_score = 0.0
    cur_itrs = 0
    cur_epochs = 1
    if opts.ckpt is not None and opts.ckpt != "" and os.path.isfile(opts.ckpt):
        # https://github.com/VainF/DeepLabV3Plus-Pytorch/issues/8#issuecomment-605601402, @PytaichukBohdan
        checkpoint = torch.load(opts.ckpt, map_location=torch.device('cpu'))
        model.load_state_dict(checkpoint["model_state"])
        model.to(device)
        model = nn.DataParallel(model)
        if opts.continue_training:
            #optimizer.load_state_dict(checkpoint["optimizer_state"])
            #kk=optimizer.param_groups[0]['lr']=0.0001
            print("+++++++++++")
            print(checkpoint["scheduler_state"])
            print("+++++++++++")
            scheduler.load_state_dict(checkpoint["scheduler_state"])
            #+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            scheduler.max_iters = opts.total_itrs
            cur_itrs = checkpoint["cur_itrs"]
            best_score = checkpoint['best_score']
            cur_epochs = checkpoint['cur_epochs']
            print(f"cur_epochs = {cur_epochs}")
            print(best_score)
            print("-----------------------------------")
            print("Training state restored from %s" % opts.ckpt)
        print("Model restored from %s" % opts.ckpt)
        del checkpoint  # free memory
    else:
        print("[!] Retrain")
        
        #model = nn.Dropout(model,p=0.2)
        model.to(device)
        model = nn.DataParallel(model)

        
    # ==========   Train Loop   ==========#
    # vis_sample_id = np.random.randint(0, len(val_loader), opts.vis_num_samples,
    #                                   np.int32) if opts.enable_vis else None  # sample idxs for visualization
    #denorm = utils.Denormalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])  # denormalization for ori images

    if opts.test_only:
        checkpoint = torch.load(opts.ckpt, map_location=torch.device('cpu'))
        cur_epochs = checkpoint['cur_epochs']
        print(f"cur_epochs = {cur_epochs}")
        model.eval()

        # Choose validation function based on display option
        if opts.show_display:
            print("Running evaluation with visual display...")
            val_score, val_loss = validateShow(
                criterion, model=model, loader=val_loader, device=device, metrics=metrics)
            ret_samples = []  # validateShow doesn't return samples
        else:
            val_score, ret_samples = validateSMI(
                opts=opts, model=model, loader=val_loader, device=device, metrics=metrics)

        print(metrics.to_str(val_score))
        
        # Performance evaluation
        if opts.eval_performance:

            # Get input size and batch size
            input_size = (opts.crop_size, opts.crop_size)
            batch_size = opts.batch_size
            iterations = 100  # Fixed iterations, sufficient for stable FPS measurement

            # Perform performance evaluation
            perf_results = evaluate_model_performance(
                model=model,
                device=device,
                val_loader=val_loader,  # Pass real data loader
                input_size=input_size,
                batch_size=batch_size,
                iterations=iterations
            )

            # Performance results are already output in evaluate_model_performance function, no file saving
            
        return

    interval_loss = 0
    es = EarlyStopping()
    #trainacc = Accuracy(task='multiclass', num_classes=opts.num_classes).to(device)
    
   
    
    while True:  # cur_itrs < opts.total_itrs:
        # =====  Train  =====
        start = time.time()
        print("Time start ....................................................")
        
        model.train()
        # total_train=0
        # correct_train=0
        
        for (images, labels) in train_loader:
            cur_itrs += 1

            images = images.to(device, dtype=torch.float32)
            labels = labels.to(device, dtype=torch.long)

            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()

            # Gradient clipping for Transformer models to prevent gradient explosion
            if any(model_type in opts.model for model_type in ['segformer', 'mask2former', 'upernet']):
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            optimizer.step()
          
            # acc=trainacc(outputs, labels)
            # print(acc)
            # accuracy
            # _, predicted = torch.max(outputs.data, 1)
            # total_train += labels.nelement()
            # correct_train += predicted.eq(labels.data).sum().item()
            
            np_loss = loss.detach().cpu().numpy()
            interval_loss += np_loss
            
            default_opts1 = { 'title': 'Loss', "showlegend":True }
            if (cur_itrs) % 50 == 0:
                
                # print(train_accuracy)
                # print("-----------------")
                #avg_accuracy = train_accuracy / len(train_loader) 
                
                interval_loss = interval_loss / 50
                print("Epoch %d, Itrs %d/%d, Loss=%f" %
                      (cur_epochs, cur_itrs, opts.total_itrs, interval_loss))
                #https://gitee.com/alsd51/visdom
                
                if (cur_epochs >= 1) and ((cur_itrs) > 50):
                    if vis is not None:
                        vis.vis_scalar("ls", "Train Loss", cur_itrs, interval_loss, default_opts1)
                interval_loss = 0.0

            # if cur_itrs == 500:
            #     end = time.time()
            #     print(f"Epoch time = {round(end - start, 2)} .....................................")
                        
            scheduler.step()
    
        #len(train_dst), len(val_dst)
        #if (cur_itrs) % opts.val_interval == 0:
        #if (cur_itrs) == len(train_dst) -1:
        save_ckpt('checkpoints/latest_%s_%s_os%d.pth' %
                  (opts.model, opts.dataset, opts.output_stride))
        print("validation...")
        model.eval()
        # model.eval()
        # pred = model(val_dst)
        # vloss = loss_fn(pred, y_test)
        # if es(model, vloss):
        #     done = True
        
        val_score, val_loss = validateLoss(
            criterion, model=model, loader=val_loader, device=device, metrics=metrics)
        
        print(f"Epoch: {cur_epochs}, tloss: {interval_loss:>7f}, vloss: {val_loss:>7f}, {es.status}")
        print(metrics.to_str(val_score))
        if val_score['Mean IoU'] > best_score:  # save best model
            best_score = val_score['Mean IoU']
            save_ckpt('checkpoints/best_%s_%s_os%d.pth' %
                      (opts.model, opts.dataset, opts.output_stride))
            
        if vis is not None:  # visualize validation score and samples
            #vis.vis_dualline("[Val] Overall Acc", cur_itrs, val_score['Overall Acc'], val_score['Mean IoU'])
            vis.vis_scalar("ls", "Val Loss", cur_itrs, val_loss, default_opts1)
            default_opts2 = { 'title': 'Val mIoU', "showlegend":False}
            vis.vis_scalar("test", "Val mIoU", cur_itrs, val_score['Mean IoU'], default_opts2)
            #vis.vis_table("[Val] Class IoU", val_score['Class IoU'])
            
            # vis.vis_scalar("[Train] Overall Acc", cur_itrs, train_score['Overall Acc'])
            # vis.vis_scalar("[Train] Mean IoU", cur_itrs, train_score['Mean IoU'])
            # vis.vis_table("[Train] Class IoU", train_score['Class IoU'])
            
            # for k, (img, target, lbl) in enumerate(ret_samples):
            #     img = (denorm(img) * 255).astype(np.uint8)
            #     target = train_dst.decode_target(target).transpose(2, 0, 1).astype(np.uint8)
            #     lbl = train_dst.decode_target(lbl).transpose(2, 0, 1).astype(np.uint8)
            #     concat_img = np.concatenate((img, target, lbl), axis=2)  # concat along width
            #     vis.vis_image('Sample %d' % k, concat_img)
        # model.train()
    
        if es(model, val_score['Mean IoU']):
            save_ckpt('checkpoints/bestEarlystop_%s_%s_os%d.pth' %
                      (opts.model, opts.dataset, opts.output_stride))
            print(f"Epoch: {cur_epochs}, tloss: {interval_loss:>7f}, vloss: {val_loss:>7f}, {es.status}")
            print("--------------------------------------------------------------------------------------")
            return

        #train_accuracy = correct_train / total_train
        # total_train=0
        # correct_train=0
        # train_score, train_samples = validate(
        #     opts=opts, model=model, loader=trainval_loader, device=device, metrics=metrics)

        # model.eval()
        # val_score, ret_samples = validate(
        #     opts=opts, model=model, loader=val_loader, device=device, metrics=metrics)

        # if vis is not None:
        #     visplotter.plot('Acc', 'train', 'Acc', cur_itrs, train_score['Overall Acc'])
        #     visplotter.plot('Acc', 'val', 'Acc', cur_itrs, val_score['Overall Acc'])
        #     vis.vis_scalar("[Val] Mean IoU", cur_itrs, val_score['Mean IoU'])
        #     vis.vis_scalar("[Train] Mean IoU", cur_itrs, train_score['Mean IoU'])
        print(f"Epoch: {cur_epochs}, tloss: {interval_loss:>7f}, vloss: {val_loss:>7f}, {es.status}")
        if cur_epochs == opts.total_epochs:
            return
        cur_epochs += 1
        end = time.time()
        print(f"Epoch time = {round(end - start, 2)} .....................................")
        
if __name__ == '__main__':
    main()
