# AI4Mars预测效果模拟器

这个工具可以根据真实的ground truth mask生成不同质量的预测mask，并使用与主程序完全相同的显示格式展示结果。

## 功能特点

### 1. 多种质量等级
- **excellent** (优秀): 95%准确率，边界清晰，噪声极少
- **good** (良好): 88%准确率，边界稍模糊，少量噪声
- **medium** (中等): 75%准确率，边界模糊，中等噪声
- **poor** (较差): 60%准确率，边界很模糊，较多噪声
- **very_poor** (很差): 45%准确率，边界非常模糊，大量噪声

### 2. 真实的预测错误模拟
- **边界模糊**: 使用形态学操作模拟分割边界不准确
- **随机噪声**: 模拟随机像素分类错误
- **类别混淆**: 模拟相似类别间的混淆（如Soil↔Sand, Bedrock↔Big Rock）
- **小物体丢失**: 模拟小区域检测失败
- **假阳性**: 模拟在背景中错误检测到物体

### 3. 完全兼容的显示格式
- 与主程序使用完全相同的显示布局
- 相同的颜色映射和图例
- 三列显示：Input Image, True Mask, Predicted Mask

## 使用方法

### 运行程序
```bash
python simulate_prediction.py
```

### 交互式模式（推荐）
1. 选择样本：随机选择或指定索引
2. 选择质量等级：从excellent到very_poor
3. 查看结果：显示预测效果和准确率

### 批量演示模式
自动展示多个样本的中等质量预测效果

## 自定义质量参数

您可以通过修改`PredictionSimulator`类中的`_get_quality_params`方法来调整质量参数：

```python
'your_custom_quality': {
    'accuracy': 0.80,           # 整体准确率
    'boundary_blur': 2,         # 边界模糊程度 (1-8)
    'noise_level': 0.08,        # 噪声水平 (0.0-0.5)
    'class_confusion': 0.10,    # 类别混淆率 (0.0-0.5)
    'small_object_loss': 0.25,  # 小物体丢失率 (0.0-0.8)
}
```

## 输出信息

程序会显示：
- 样本索引和基本信息
- 预测质量等级
- 预测准确率
- 可视化结果（与主程序格式完全一致）

## 应用场景

1. **演示用途**: 展示不同质量的预测效果
2. **算法对比**: 模拟不同算法的性能差异
3. **教学用途**: 展示语义分割中常见的错误类型
4. **测试显示**: 验证显示格式的正确性

## 技术细节

### 模拟算法
- 使用scipy和skimage进行形态学操作
- 基于连通区域分析进行小物体处理
- 使用概率模型进行类别混淆
- 考虑AI4Mars数据集的特定类别关系

### 颜色映射
完全使用AI4Mars数据集的颜色方案：
- Soil: 绿色 (0x56, 0x94, 0x1E)
- Bedrock: 紫色 (0xA0, 0x20, 0xF0)
- Sand: 黄色 (0xCC, 0xFF, 0x42)
- Big Rock: 红色 (0xFF, 0x00, 0x00)
- Background: 青蓝色 (0x42, 0xCC, 0xFF)

## 注意事项

1. 确保AI4Mars数据集已正确安装在`./datasets/data/`目录下
2. 需要安装相关依赖：scipy, skimage, PIL, matplotlib
3. 模拟的预测效果基于统计模型，可能与实际模型行为有差异
4. 可以根据需要调整质量参数以获得更符合实际情况的效果

## 扩展功能

您可以轻松扩展此工具：
- 添加新的质量等级
- 修改类别混淆矩阵
- 调整边界模糊算法
- 添加更多的错误类型模拟
