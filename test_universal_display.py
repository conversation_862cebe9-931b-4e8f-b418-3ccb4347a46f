#!/usr/bin/env python3
"""
Test script to verify the universal display functionality works with different datasets
"""

import sys
import os
import torch
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

# Add the current directory to Python path
sys.path.append('.')

# Import the modified functions
from main import get_dataset_info, decode_target_universal, display
from datasets.ai4mars import AI4Mars
from datasets.labelmars6 import LabelMars6
from utils import ext_transforms as et

def test_dataset_info_extraction():
    """Test dataset information extraction for different datasets"""
    print("Testing dataset information extraction...")
    
    data_root = './datasets/data/'
    
    # Test AI4Mars
    try:
        val_transform = et.ExtCompose([et.ExtResize(512), et.ExtToTensor()])
        ai4mars_dst = AI4Mars(root=data_root, split='testM3', transform=val_transform)
        
        class_names, class_colors, num_classes, dataset_name = get_dataset_info(ai4mars_dst)
        
        print(f"\n=== {dataset_name} ===")
        print(f"Number of classes: {num_classes}")
        print(f"Class names: {class_names}")
        print(f"Class colors: {class_colors}")
        
        # Test color mapping
        test_mask = np.array([[0, 1, 2, 3, 255]], dtype=np.uint8)
        colored_mask = decode_target_universal(test_mask, ai4mars_dst)
        print(f"Color mapping test - shape: {colored_mask.shape}")
        
    except Exception as e:
        print(f"AI4Mars test failed: {e}")
    
    # Test LabelMars6
    try:
        labelmars_dst = LabelMars6(root=data_root, split='test', transform=val_transform)
        
        class_names, class_colors, num_classes, dataset_name = get_dataset_info(labelmars_dst)
        
        print(f"\n=== {dataset_name} ===")
        print(f"Number of classes: {num_classes}")
        print(f"Class names: {class_names}")
        print(f"Class colors: {class_colors}")
        
        # Test color mapping
        test_mask = np.array([[0, 1, 2, 3, 4, 255]], dtype=np.uint8)
        colored_mask = decode_target_universal(test_mask, labelmars_dst)
        print(f"Color mapping test - shape: {colored_mask.shape}")
        
    except Exception as e:
        print(f"LabelMars6 test failed: {e}")

def create_test_display():
    """Create a test display to verify the layout works"""
    print("\nTesting universal display layout...")
    
    data_root = './datasets/data/'
    val_transform = et.ExtCompose([et.ExtResize(512), et.ExtToTensor()])
    
    try:
        # Test with AI4Mars (5 classes)
        ai4mars_dst = AI4Mars(root=data_root, split='testM3', transform=val_transform)
        
        # Create dummy data
        dummy_image = torch.randn(1, 3, 512, 512)
        dummy_target = np.random.randint(0, 5, (1, 512, 512))
        dummy_pred = np.random.randint(0, 5, (1, 512, 512))
        
        # Add some 255 values
        dummy_target[0, 100:200, 100:200] = 255
        dummy_pred[0, 150:250, 150:250] = 255
        
        # Create a mock loader
        class MockLoader:
            def __init__(self, dataset):
                self.dataset = dataset
        
        mock_loader = MockLoader(ai4mars_dst)
        
        # Test display
        display_list = [dummy_image, dummy_target, dummy_pred]
        
        print("Displaying test layout for AI4Mars (5 classes)...")
        display(display_list, mock_loader)
        
    except Exception as e:
        print(f"Display test failed: {e}")

if __name__ == "__main__":
    test_dataset_info_extraction()
    create_test_display()
    print("\n✅ Universal display system test completed!")
