#!/usr/bin/env python3
"""
Test script to verify the original display logic works correctly
"""

import sys
import os
import torch
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

# Add the current directory to Python path
sys.path.append('.')

# Import the modified functions
from main import decode_target_original_style
from datasets.ai4mars import AI4Mars
from utils import ext_transforms as et

def test_original_display_logic():
    """Test the original display logic"""
    
    print("Testing original display logic...")
    
    # Create test data with all classes including 255
    test_mask = np.array([
        [0, 1, 2, 3, 255],
        [255, 0, 1, 2, 3],
        [3, 255, 0, 1, 2],
        [2, 3, 255, 0, 1],
        [1, 2, 3, 255, 0]
    ], dtype=np.uint8)
    
    print(f"Test mask shape: {test_mask.shape}")
    print(f"Unique values: {np.unique(test_mask)}")
    
    # Test the original decode function
    colored_mask = decode_target_original_style(test_mask)
    
    print(f"Colored mask shape: {colored_mask.shape}")
    print(f"Color range: {colored_mask.min()} - {colored_mask.max()}")
    
    # Create the exact same layout as original code
    IMAGE_SIZE = 512
    fig, ax_dict = plt.subplot_mosaic(
        [
            [0, 1, 2, 3],
            [0, 1, 2, 4],
            [0, 1, 2, 5],
            [0, 1, 2, 6],
            [0, 1, 2, 7]
        ], figsize=(10, 2))
    
    # Display the test mask
    ax_dict[0].axis("off")
    ax_dict[0].set_title("Test Mask", fontsize=10)
    ax_dict[0].imshow(colored_mask)
    
    # Hide unused axes
    ax_dict[1].axis("off")
    ax_dict[2].axis("off")
    
    # Legend (exactly like original)
    titlelbl = ["Soil", "Bed Rock", "Sand", "Big Rock", "Background"]
    colors = [(0x56, 0x94, 0x1E), (0xA0, 0x20, 0xF0), (0xCC, 0xFF, 0x42), (0xFF, 0x00, 0x00), (0x42, 0xCC, 0xFF)]
    xpos = -2
    for i in range(3, 8):
        ax_dict[i].axis("off")
        ax_dict[i].set_title(titlelbl[i-3], x=xpos, y=0.0, fontsize=10)
        img = Image.new(mode="RGB", size=(IMAGE_SIZE, IMAGE_SIZE), color=colors[i-3])
        ax_dict[i].imshow(img)
    
    plt.suptitle("Original Display Logic Test", fontsize=14, fontweight='bold')
    plt.show()
    
    # Verify colors manually
    print("\nVerifying colors:")
    print(f"Position (0,0) - Class 0 (Soil): {colored_mask[0,0]} - Expected: [86, 148, 30]")
    print(f"Position (0,1) - Class 1 (Bedrock): {colored_mask[0,1]} - Expected: [160, 32, 240]")
    print(f"Position (0,2) - Class 2 (Sand): {colored_mask[0,2]} - Expected: [204, 255, 66]")
    print(f"Position (0,3) - Class 3 (Big Rock): {colored_mask[0,3]} - Expected: [255, 0, 0]")
    print(f"Position (0,4) - Class 255 (Background): {colored_mask[0,4]} - Expected: [66, 204, 255]")
    
    return True

if __name__ == "__main__":
    success = test_original_display_logic()
    if success:
        print("\n✅ Original display logic test completed!")
    else:
        print("\n❌ Original display logic test failed!")
