#!/usr/bin/env python3
"""
Test script to verify the display modifications work correctly
"""

import sys
import os
import torch
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

# Add the current directory to Python path
sys.path.append('.')

# Import the modified functions
from main import display_samples_by_iou, calculate_sample_iou, decode_target_fixed
from datasets.ai4mars import AI4Mars
from utils import ext_transforms as et

def test_color_mapping():
    """Test the fixed color mapping"""
    print("Testing fixed color mapping...")

    # Create test data with all classes including 255
    test_mask = np.array([
        [0, 1, 2, 3, 255],
        [255, 0, 1, 2, 3],
        [3, 255, 0, 1, 2],
        [2, 3, 255, 0, 1],
        [1, 2, 3, 255, 0]
    ])

    # Create dummy dataset
    data_root = './datasets/data/'
    val_transform = et.ExtCompose([
        et.ExtResize(512),
        et.ExtToTensor(),
    ])

    try:
        val_dst = AI4Mars(root=data_root, split='testM3', transform=val_transform)

        # Test the fixed decode function
        colored_mask = decode_target_fixed(test_mask, val_dst)

        print(f"Original mask shape: {test_mask.shape}")
        print(f"Colored mask shape: {colored_mask.shape}")
        print(f"Unique values in original: {np.unique(test_mask)}")
        print(f"Color range: {colored_mask.min()} - {colored_mask.max()}")

        # Check if 255 values are properly mapped to cyan
        mask_255_positions = (test_mask == 255)
        cyan_color = np.array([0x42, 0xCC, 0xFF])

        # Check if all 255 positions have cyan color
        for i in range(test_mask.shape[0]):
            for j in range(test_mask.shape[1]):
                if test_mask[i, j] == 255:
                    actual_color = colored_mask[i, j]
                    if not np.array_equal(actual_color, cyan_color):
                        print(f"❌ Color mismatch at ({i},{j}): expected {cyan_color}, got {actual_color}")
                        return False

        print("✅ Color mapping test passed!")

        # Display the test mask
        plt.figure(figsize=(8, 6))
        plt.imshow(colored_mask)
        plt.title("Test Color Mapping\n(Should show: Green, Purple, Yellow, Red, Cyan)")
        plt.axis('off')

        # Add legend
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor=(0x56/255, 0x94/255, 0x1E/255), label='Soil'),
            Patch(facecolor=(0xA0/255, 0x20/255, 0xF0/255), label='Bed Rock'),
            Patch(facecolor=(0xCC/255, 0xFF/255, 0x42/255), label='Sand'),
            Patch(facecolor=(0xFF/255, 0x00/255, 0x00/255), label='Big Rock'),
            Patch(facecolor=(0x42/255, 0xCC/255, 0xFF/255), label='Background')
        ]
        plt.legend(handles=legend_elements, loc='center left', bbox_to_anchor=(1, 0.5))
        plt.tight_layout()
        plt.show()

        return True

    except Exception as e:
        print(f"Error during testing: {e}")
        return False

def test_display_modifications():
    """Test the modified display functions"""

    print("Testing display modifications...")

    # Create a simple test dataset
    data_root = './datasets/data/'

    # Check if AI4Mars dataset exists
    if not os.path.exists(os.path.join(data_root, 'AI4Mars')):
        print("AI4Mars dataset not found. Please ensure the dataset is available.")
        return False

    # Create dataset with simple transforms
    val_transform = et.ExtCompose([
        et.ExtResize(512),
        et.ExtToTensor(),
    ])

    try:
        val_dst = AI4Mars(root=data_root, split='testM3', transform=val_transform)
        print(f"Dataset loaded successfully. Found {len(val_dst)} samples.")

        # Test the color mapping
        print("\nTesting color mapping...")
        print(f"Classes: {[c.name for c in val_dst.classes if c.train_id != -1 and c.train_id != 255]}")
        print(f"Original colors: {val_dst.train_id_to_color}")

        # Test sample IoU calculation
        print("\nTesting IoU calculation...")
        dummy_target = np.random.randint(0, 4, (100, 100))
        dummy_pred = np.random.randint(0, 4, (100, 100))
        iou = calculate_sample_iou(dummy_target, dummy_pred, 4)
        print(f"Sample IoU calculation works: {iou:.4f}")

        # Test color mapping
        return test_color_mapping()

    except Exception as e:
        print(f"Error during testing: {e}")
        return False

if __name__ == "__main__":
    success = test_display_modifications()
    if success:
        print("\n✅ All display modifications tests passed!")
    else:
        print("\n❌ Display modifications test failed!")
