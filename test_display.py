#!/usr/bin/env python3
"""
Test script to verify the display modifications work correctly
"""

import sys
import os
import torch
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

# Add the current directory to Python path
sys.path.append('.')

# Import the modified functions
from main import display_samples_by_iou, calculate_sample_iou
from datasets.ai4mars import AI4Mars
from utils import ext_transforms as et

def test_display_modifications():
    """Test the modified display functions"""
    
    print("Testing display modifications...")
    
    # Create a simple test dataset
    data_root = './datasets/data/'
    
    # Check if AI4Mars dataset exists
    if not os.path.exists(os.path.join(data_root, 'AI4Mars')):
        print("AI4Mars dataset not found. Please ensure the dataset is available.")
        return False
    
    # Create dataset with simple transforms
    val_transform = et.ExtCompose([
        et.ExtResize(512),
        et.ExtToTensor(),
    ])
    
    try:
        val_dst = AI4Mars(root=data_root, split='testM3', transform=val_transform)
        print(f"Dataset loaded successfully. Found {len(val_dst)} samples.")
        
        # Test the color mapping
        print("\nTesting color mapping...")
        print(f"Classes: {[c.name for c in val_dst.classes if c.train_id != -1 and c.train_id != 255]}")
        print(f"Colors: {val_dst.train_id_to_color}")
        
        # Test sample IoU calculation
        print("\nTesting IoU calculation...")
        dummy_target = np.random.randint(0, 4, (100, 100))
        dummy_pred = np.random.randint(0, 4, (100, 100))
        iou = calculate_sample_iou(dummy_target, dummy_pred, 4)
        print(f"Sample IoU calculation works: {iou:.4f}")
        
        return True
        
    except Exception as e:
        print(f"Error during testing: {e}")
        return False

if __name__ == "__main__":
    success = test_display_modifications()
    if success:
        print("\n✅ Display modifications test passed!")
    else:
        print("\n❌ Display modifications test failed!")
