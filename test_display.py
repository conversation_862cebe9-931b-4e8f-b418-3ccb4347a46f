#!/usr/bin/env python3
"""
Test script to verify the improved display function works correctly
with different datasets and color mappings.
"""

import sys
import os
import numpy as np
import torch
from PIL import Image
import matplotlib.pyplot as plt

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datasets.jiayu import JiayuDataset
from datasets.ai4mars import AI4Mars
from datasets.labelmars6 import LabelMars6

def test_dataset_info_extraction():
    """Test the get_dataset_info function with different datasets"""
    
    # Import the functions from main.py
    from main import get_dataset_info, generate_colors, apply_color_mapping
    
    print("=" * 60)
    print("TESTING DATASET INFO EXTRACTION")
    print("=" * 60)
    
    # Test datasets (if they exist)
    datasets_to_test = []
    
    # Test Jiayu dataset
    try:
        jiayu_dataset = JiayuDataset(root='./datasets/data/', split='test')
        datasets_to_test.append(('Jiayu', jiayu_dataset))
        print(f"✓ Jiayu dataset loaded: {len(jiayu_dataset)} samples")
    except Exception as e:
        print(f"✗ Jiayu dataset failed: {e}")
    
    # Test AI4Mars dataset (if available)
    try:
        ai4mars_dataset = AI4Mars(root='./datasets/data/', split='test')
        datasets_to_test.append(('AI4Mars', ai4mars_dataset))
        print(f"✓ AI4Mars dataset loaded: {len(ai4mars_dataset)} samples")
    except Exception as e:
        print(f"✗ AI4Mars dataset failed: {e}")
    
    # Test each dataset
    for name, dataset in datasets_to_test:
        print(f"\n--- Testing {name} Dataset ---")
        try:
            class_names, class_colors, num_classes, dataset_name = get_dataset_info(dataset)
            
            print(f"Dataset Name: {dataset_name}")
            print(f"Number of Classes: {num_classes}")
            print(f"Class Names: {class_names}")
            print(f"Class Colors: {class_colors}")
            
            # Test color mapping
            test_mask = np.random.randint(0, num_classes, (100, 100))
            colored_mask = apply_color_mapping(test_mask, class_colors)
            print(f"Color mapping test: {colored_mask.shape}, dtype: {colored_mask.dtype}")
            
            # Verify color mapping correctness
            unique_classes = np.unique(test_mask)
            print(f"Unique classes in test mask: {unique_classes}")
            
            for class_id in unique_classes:
                mask_pixels = (test_mask == class_id)
                if np.any(mask_pixels):
                    expected_color = class_colors[class_id]
                    actual_color = colored_mask[mask_pixels][0]  # Get first pixel of this class
                    print(f"  Class {class_id}: Expected {expected_color}, Got {actual_color}")
                    
                    # Check if colors match
                    if np.array_equal(expected_color, actual_color):
                        print(f"    ✓ Color mapping correct for class {class_id}")
                    else:
                        print(f"    ✗ Color mapping incorrect for class {class_id}")
            
        except Exception as e:
            print(f"✗ Error testing {name}: {e}")
            import traceback
            traceback.print_exc()

def test_color_generation():
    """Test automatic color generation"""
    from main import generate_colors
    
    print(f"\n--- Testing Color Generation ---")
    
    for num_classes in [2, 5, 10]:
        colors = generate_colors(num_classes)
        print(f"Generated {num_classes} colors: {colors}")
        
        # Check if colors are distinct
        unique_colors = len(set(tuple(c) for c in colors))
        if unique_colors == num_classes:
            print(f"  ✓ All {num_classes} colors are unique")
        else:
            print(f"  ✗ Only {unique_colors} unique colors out of {num_classes}")

def create_test_visualization():
    """Create a test visualization to verify display function"""
    from main import display
    
    print(f"\n--- Creating Test Visualization ---")
    
    # Create mock data
    class MockDataset:
        def __init__(self, num_classes=2):
            self.num_classes = num_classes
            self.class_names = ['Background', 'Rock'] if num_classes == 2 else [f'Class {i}' for i in range(num_classes)]
        
        def get_class_names(self):
            return self.class_names
        
        def get_class_colors(self):
            if self.num_classes == 2:
                return [[0, 0, 0], [0, 255, 13]]  # Black, Green
            else:
                from main import generate_colors
                return generate_colors(self.num_classes)
    
    class MockLoader:
        def __init__(self, dataset):
            self.dataset = dataset
    
    # Create test data
    mock_dataset = MockDataset(num_classes=2)
    mock_loader = MockLoader(mock_dataset)
    
    # Create synthetic image, true mask, and predicted mask
    height, width = 256, 256
    
    # Synthetic RGB image (normalized)
    image_tensor = torch.rand(3, height, width)  # CHW format
    
    # True mask (ground truth)
    true_mask = np.zeros((height, width), dtype=np.uint8)
    true_mask[50:150, 50:150] = 1  # Rock region
    
    # Predicted mask (with some errors)
    pred_mask = true_mask.copy()
    pred_mask[60:140, 60:140] = 1  # Slightly different prediction
    pred_mask[200:220, 200:220] = 1  # False positive
    
    # Create display list
    display_list = [
        [image_tensor],  # Input images (batch of 1)
        [true_mask],     # True masks (batch of 1)
        [pred_mask]      # Predicted masks (batch of 1)
    ]
    
    print("Creating test visualization...")
    print("Note: This will show a matplotlib window. Close it to continue.")
    
    try:
        # This should work with our improved display function
        display(display_list, mock_loader)
        print("✓ Test visualization completed successfully")
    except Exception as e:
        print(f"✗ Test visualization failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    print("Testing Improved Display Function")
    print("=" * 60)
    
    # Run tests
    test_dataset_info_extraction()
    test_color_generation()
    
    # Ask user if they want to see test visualization
    response = input("\nDo you want to see a test visualization? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        create_test_visualization()
    
    print("\n" + "=" * 60)
    print("Testing completed!")
